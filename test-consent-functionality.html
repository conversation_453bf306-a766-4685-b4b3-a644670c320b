<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test GDPR Consent Functionality - HAI Systems</title>
    <style>
        body {
            font-family: 'Space Grotesk', sans-serif;
            background: #0a0a0a;
            color: #e2e8f0;
            padding: 2rem;
            line-height: 1.6;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 12px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #ff0040, #cc0033);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            margin: 0.5rem;
            font-weight: 500;
        }
        
        .test-button:hover {
            transform: translateY(-1px);
        }
        
        .status {
            padding: 0.5rem;
            border-radius: 4px;
            margin: 0.5rem 0;
        }
        
        .status.success {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
        }
        
        .status.error {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }
        
        .status.info {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }
        
        pre {
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test GDPR Consent Functionality</h1>
        <p>Cette page permet de tester toutes les fonctionnalités du système de consentement GDPR.</p>
        
        <div class="test-section">
            <h2>🍪 État actuel du consentement</h2>
            <div id="consent-status"></div>
            <button class="test-button" onclick="checkConsentStatus()">Vérifier l'état</button>
        </div>
        
        <div class="test-section">
            <h2>🎛️ Actions de test</h2>
            <button class="test-button" onclick="clearConsent()">Effacer le consentement</button>
            <button class="test-button" onclick="showBanner()">Afficher la bannière</button>
            <button class="test-button" onclick="showSettings()">Ouvrir les paramètres</button>
            <button class="test-button" onclick="acceptAll()">Accepter tout</button>
            <button class="test-button" onclick="acceptEssentialOnly()">Essentiel seulement</button>
            <button class="test-button" onclick="withdrawConsent()">Retirer le consentement</button>
        </div>
        
        <div class="test-section">
            <h2>📊 Test Analytics</h2>
            <button class="test-button" onclick="testAnalytics()">Tester Google Analytics</button>
            <div id="analytics-status"></div>
        </div>
        
        <div class="test-section">
            <h2>🔍 Informations techniques</h2>
            <button class="test-button" onclick="showTechnicalInfo()">Afficher les infos</button>
            <div id="technical-info"></div>
        </div>
        
        <div class="test-section">
            <h2>📝 Journal des événements</h2>
            <button class="test-button" onclick="clearLog()">Effacer le journal</button>
            <div id="event-log"></div>
        </div>
    </div>

    <script>
        // Load the main site's consent system
        const script = document.createElement('script');
        script.src = '/index.html';
        
        // Initialize logging
        let eventLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            eventLog.push({ timestamp, message, type });
            updateEventLog();
        }
        
        function updateEventLog() {
            const logDiv = document.getElementById('event-log');
            logDiv.innerHTML = eventLog.slice(-10).map(entry => 
                `<div class="status ${entry.type}">[${entry.timestamp}] ${entry.message}</div>`
            ).join('');
        }
        
        function checkConsentStatus() {
            try {
                if (window.HAICookies) {
                    const status = window.HAICookies.getConsentStatus();
                    document.getElementById('consent-status').innerHTML = `
                        <pre>${JSON.stringify(status, null, 2)}</pre>
                    `;
                    log('État du consentement vérifié', 'success');
                } else {
                    document.getElementById('consent-status').innerHTML = 
                        '<div class="status error">Système de consentement non disponible</div>';
                    log('Système de consentement non disponible', 'error');
                }
            } catch (error) {
                log(`Erreur lors de la vérification: ${error.message}`, 'error');
            }
        }
        
        function clearConsent() {
            try {
                localStorage.removeItem('hai_consent');
                log('Consentement effacé du localStorage', 'success');
                checkConsentStatus();
            } catch (error) {
                log(`Erreur lors de l'effacement: ${error.message}`, 'error');
            }
        }
        
        function showBanner() {
            try {
                if (window.HAICookies) {
                    window.HAICookies.showBanner();
                    log('Bannière de consentement affichée', 'success');
                } else {
                    log('Système de consentement non disponible', 'error');
                }
            } catch (error) {
                log(`Erreur lors de l'affichage: ${error.message}`, 'error');
            }
        }
        
        function showSettings() {
            try {
                if (window.haiConsent) {
                    window.haiConsent.showConsentSettings();
                    log('Paramètres de consentement ouverts', 'success');
                } else {
                    log('Système de consentement non disponible', 'error');
                }
            } catch (error) {
                log(`Erreur lors de l'ouverture: ${error.message}`, 'error');
            }
        }
        
        function acceptAll() {
            try {
                if (window.HAICookies) {
                    window.HAICookies.acceptAll();
                    log('Tous les cookies acceptés', 'success');
                    setTimeout(checkConsentStatus, 500);
                } else {
                    log('Système de consentement non disponible', 'error');
                }
            } catch (error) {
                log(`Erreur lors de l'acceptation: ${error.message}`, 'error');
            }
        }
        
        function acceptEssentialOnly() {
            try {
                if (window.HAICookies) {
                    window.HAICookies.acceptEssentialOnly();
                    log('Cookies essentiels seulement acceptés', 'success');
                    setTimeout(checkConsentStatus, 500);
                } else {
                    log('Système de consentement non disponible', 'error');
                }
            } catch (error) {
                log(`Erreur lors de l'acceptation: ${error.message}`, 'error');
            }
        }
        
        function withdrawConsent() {
            try {
                if (window.HAICookies) {
                    window.HAICookies.withdrawConsent();
                    log('Consentement retiré', 'success');
                    setTimeout(checkConsentStatus, 500);
                } else {
                    log('Système de consentement non disponible', 'error');
                }
            } catch (error) {
                log(`Erreur lors du retrait: ${error.message}`, 'error');
            }
        }
        
        function testAnalytics() {
            try {
                const analyticsDiv = document.getElementById('analytics-status');
                const hasConsent = window.HAICookies ? window.HAICookies.hasConsent('analytics') : false;
                const gtagAvailable = typeof gtag === 'function';
                
                analyticsDiv.innerHTML = `
                    <div class="status ${hasConsent ? 'success' : 'error'}">
                        Consentement Analytics: ${hasConsent ? 'Accordé' : 'Refusé'}
                    </div>
                    <div class="status ${gtagAvailable ? 'success' : 'error'}">
                        Google Analytics: ${gtagAvailable ? 'Chargé' : 'Non chargé'}
                    </div>
                `;
                
                if (hasConsent && gtagAvailable) {
                    window.HAICookies.track('test_event', { test: true });
                    log('Événement de test envoyé à Analytics', 'success');
                } else {
                    log('Analytics non disponible ou consentement refusé', 'info');
                }
            } catch (error) {
                log(`Erreur lors du test Analytics: ${error.message}`, 'error');
            }
        }
        
        function showTechnicalInfo() {
            try {
                const info = {
                    userAgent: navigator.userAgent,
                    cookiesEnabled: navigator.cookieEnabled,
                    localStorage: typeof Storage !== 'undefined',
                    haiConsentAvailable: typeof window.haiConsent !== 'undefined',
                    haiCookiesAvailable: typeof window.HAICookies !== 'undefined',
                    gtagAvailable: typeof gtag === 'function',
                    currentCookies: document.cookie
                };
                
                document.getElementById('technical-info').innerHTML = `
                    <pre>${JSON.stringify(info, null, 2)}</pre>
                `;
                log('Informations techniques affichées', 'success');
            } catch (error) {
                log(`Erreur lors de l'affichage des infos: ${error.message}`, 'error');
            }
        }
        
        function clearLog() {
            eventLog = [];
            updateEventLog();
        }
        
        // Initialize on load
        document.addEventListener('DOMContentLoaded', function() {
            log('Page de test chargée', 'success');
            
            // Wait for consent system to load
            setTimeout(() => {
                if (window.HAICookies) {
                    log('Système de consentement HAI détecté', 'success');
                    checkConsentStatus();
                } else {
                    log('Système de consentement HAI non détecté', 'error');
                }
            }, 1000);
        });
        
        // Monitor consent changes
        if (window.HAICookies) {
            window.HAICookies.onConsentChange((data) => {
                log(`Consentement modifié: ${data.category} = ${data.granted}`, 'info');
                checkConsentStatus();
            });
        }
    </script>
    
    <!-- Load the main consent system -->
    <iframe src="/index.html" style="display: none;" onload="
        try {
            if (this.contentWindow.haiConsent) {
                window.haiConsent = this.contentWindow.haiConsent;
                window.HAICookies = this.contentWindow.HAICookies;
                window.gtag = this.contentWindow.gtag;
                console.log('Consent system loaded from iframe');
            }
        } catch (e) {
            console.warn('Could not access iframe content:', e);
        }
    "></iframe>
</body>
</html>
